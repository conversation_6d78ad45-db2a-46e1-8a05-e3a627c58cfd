/*
Theme Name: Odessa Dentists
Description: Custom WordPress theme for Odessa dental directory website
Version: 1.0
Author: Custom Development
*/

/* CSS Variables */
:root {
    /* Colors */
    --color-primary: #2c5aa0;
    --color-primary-dark: #1e3d72;
    --color-secondary: #6c757d;
    --color-secondary-dark: #545b62;
    --color-success: #28a745;
    --color-warning: #ffc107;
    --color-danger: #dc3545;
    --color-text: #333;
    --color-text-light: #666;
    --color-text-lighter: #ccc;
    --color-white: #fff;
    --color-background: #f8f9fa;
    --color-background-light: #e9ecef;
    --color-border: #ddd;
    --color-border-light: #e9ecef;
    --color-shadow: rgba(0, 0, 0, 0.1);
    --color-shadow-hover: rgba(0, 0, 0, 0.15);

    /* Font Sizes */
    --font-size-xs: 0.8rem;
    --font-size-sm: 0.9rem;
    --font-size-base: 1rem;
    --font-size-md: 1.1rem;
    --font-size-lg: 1.2rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 2rem;
    --font-size-3xl: 3rem;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;

    /* Transitions */
    --transition-fast: 0.3s;

    /* Shadows */
    --shadow-sm: 0 2px 5px var(--color-shadow);
    --shadow-md: 0 2px 10px var(--color-shadow);
    --shadow-lg: 0 5px 20px var(--color-shadow-hover);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--color-text);
    background-color: var(--color-background);
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-xl);
}

/* Header Block */
.header {
    background: var(--color-white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
}

.header__title {
    font-size: var(--font-size-2xl);
    font-weight: bold;
    color: var(--color-primary);
    text-decoration: none;
}

/* Navigation Block */
.navigation__list {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
}

.navigation__link {
    text-decoration: none;
    color: var(--color-text);
    font-weight: 500;
    transition: color var(--transition-fast);
}

.navigation__link:hover {
    color: var(--color-primary);
}

/* Main Content */
.main {
    padding: var(--spacing-xl) 0;
    min-height: calc(100vh - 200px);
}

/* Directory Grid Block */
.directory-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

/* Listing Card Block */
.listing-card {
    background: var(--color-white);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.listing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.listing-card__title {
    font-size: var(--font-size-xl);
    font-weight: bold;
    margin-bottom: var(--spacing-sm);
    color: var(--color-primary);
}

.listing-card__meta {
    color: var(--color-text-light);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
}

.listing-card__address {
    margin-bottom: var(--spacing-sm);
}

.listing-card__phone {
    margin-bottom: var(--spacing-sm);
}

.listing-card__rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.listing-card__stars {
    color: var(--color-warning);
}

/* Single Listing Block */
.listing-single__header {
    background: var(--color-white);
    padding: var(--spacing-xl) 0;
    margin-bottom: var(--spacing-xl);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
}

.listing-single__grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.listing-single__details {
    background: var(--color-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
}

.listing-single__sidebar {
    background: var(--color-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    height: fit-content;
}

/* Info Section Block */
.info-section {
    margin-bottom: var(--spacing-xl);
}

.info-section__title {
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--color-border-light);
    padding-bottom: var(--spacing-sm);
}

/* Working Hours Block */
.working-hours {
    list-style: none;
}

.working-hours__item {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--color-border-light);
}

.working-hours__day {
    font-weight: 500;
    text-transform: capitalize;
}

.working-hours__time {
    color: var(--color-text-light);
}

.working-hours__time--closed {
    color: var(--color-danger);
}

.working-hours__time--open {
    color: var(--color-success);
}

/* Social Links Block */
.social-links {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.social-links__item {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--color-primary);
    color: var(--color-white);
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    transition: background var(--transition-fast);
}

.social-links__item:hover {
    background: var(--color-primary-dark);
}

/* Contact Info Block */
.contact-info {
    list-style: none;
}

.contact-info__item {
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* Search Form Block */
.search-form {
    background: var(--color-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);
}

.search-form__row {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    flex-wrap: wrap;
}

.search-form__input,
.search-form__select {
    padding: 0.75rem;
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-base);
}

.search-form__button {
    background: var(--color-primary);
    color: var(--color-white);
    border: none;
    padding: 0.75rem var(--spacing-lg);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: background var(--transition-fast);
}

.search-form__button:hover {
    background: var(--color-primary-dark);
}

/* Footer Block */
.footer {
    background: var(--color-text);
    color: var(--color-white);
    text-align: center;
    padding: var(--spacing-xl) 0;
    margin-top: var(--spacing-2xl);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header__content {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .navigation__list {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .directory-grid {
        grid-template-columns: 1fr;
    }

    .listing-single__grid {
        grid-template-columns: 1fr;
    }

    .search-form__row {
        flex-direction: column;
        align-items: stretch;
    }

    .search-form__input,
    .search-form__select,
    .search-form__button {
        width: 100%;
    }
}

/* Utility Classes */
.u-text-center {
    text-align: center;
}

.u-mb-sm { margin-bottom: var(--spacing-sm); }
.u-mb-md { margin-bottom: var(--spacing-md); }
.u-mb-lg { margin-bottom: var(--spacing-lg); }
.u-mb-xl { margin-bottom: var(--spacing-xl); }

.u-mt-sm { margin-top: var(--spacing-sm); }
.u-mt-md { margin-top: var(--spacing-md); }
.u-mt-lg { margin-top: var(--spacing-lg); }
.u-mt-xl { margin-top: var(--spacing-xl); }

/* Button Component */
.btn {
    display: inline-block;
    padding: 0.75rem var(--spacing-lg);
    background: var(--color-primary);
    color: var(--color-white);
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    transition: background var(--transition-fast);
    border: none;
    cursor: pointer;
    font-size: var(--font-size-base);
}

.btn:hover {
    background: var(--color-primary-dark);
}

.btn--secondary {
    background: var(--color-secondary);
}

.btn--secondary:hover {
    background: var(--color-secondary-dark);
}

/* Hero Section Block */
.hero {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    color: var(--color-white);
    padding: var(--spacing-3xl) var(--spacing-xl);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-2xl);
}

.hero__title {
    font-size: var(--font-size-3xl);
    font-weight: bold;
    margin-bottom: var(--spacing-md);
}

.hero__description {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
}

.hero__search {
    max-width: 600px;
    margin: 0 auto;
}

.hero__search-row {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.hero__search-input {
    flex: 1;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-base);
}

.hero__search-button {
    background: var(--color-white);
    color: var(--color-primary);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius-sm);
    font-weight: bold;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.hero__search-button:hover {
    background: var(--color-background);
    transform: translateY(-2px);
}

/* Categories Grid Block */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

/* Category Card Block */
.category-card {
    background: var(--color-white);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.category-card__link {
    text-decoration: none;
    color: inherit;
}

.category-card__title {
    color: var(--color-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-md);
}

.category-card__count {
    color: var(--color-text-light);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
}

.category-card__description {
    color: var(--color-text-light);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

/* Section Title Component */
.section-title {
    font-size: var(--font-size-2xl);
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
}

/* Badge Component */
.badge {
    background: var(--color-success);
    color: var(--color-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    margin-left: var(--spacing-sm);
}

/* Listing Image Block */
.listing-card__image {
    position: relative;
    height: 200px;
    overflow: hidden;
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.listing-card__image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.listing-card__image .badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
}

/* Action Buttons Block */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.action-buttons .btn {
    text-align: center;
}

/* Results Summary Block */
.results-summary {
    background: var(--color-background-light);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-xl);
}

.results-summary__title {
    margin-bottom: var(--spacing-sm);
    color: var(--color-primary);
}

.results-summary__clear-link {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: 500;
}

.results-summary__clear-link:hover {
    text-decoration: underline;
}

/* Category Lists */
.category-list {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
    margin-top: var(--spacing-md);
}

/* Search Result Block */
.search-result {
    background: var(--color-white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-md);
}

.search-result__title-link {
    color: var(--color-primary);
    text-decoration: none;
}

.search-result__title-link:hover {
    text-decoration: underline;
}

.search-result__meta {
    color: var(--color-text-light);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
}

/* CTA Section Block */
.cta {
    background: var(--color-background);
    padding: var(--spacing-2xl) var(--spacing-xl);
    border-radius: var(--border-radius-md);
    border: 2px dashed var(--color-primary);
}

.cta__title {
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
}

.cta__description {
    color: var(--color-text-light);
    margin-bottom: var(--spacing-lg);
}

/* Footer Content Block */
.footer__content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer__title {
    color: var(--color-white);
    margin-bottom: var(--spacing-md);
}

.footer__list {
    list-style: none;
}

.footer__list-item {
    margin-bottom: var(--spacing-sm);
}

.footer__link {
    color: var(--color-text-lighter);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer__link:hover {
    color: var(--color-white);
}

.footer__bottom {
    border-top: 1px solid #555;
    padding-top: var(--spacing-md);
    text-align: center;
    color: var(--color-text-lighter);
}

.footer__bottom-link {
    color: var(--color-text-lighter);
    text-decoration: none;
}

.footer__bottom-link:hover {
    color: var(--color-white);
}
