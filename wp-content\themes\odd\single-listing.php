<?php
/**
 * The template for displaying single listing posts
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        
        <?php while (have_posts()) : the_post(); ?>
            
            <?php
            // Get all listing data (updated field names)
            $listing_name = get_the_title(); // Use post title instead of listing_name
            $listing_phone = get_post_meta(get_the_ID(), 'listing_phone', true);
            $listing_email = get_post_meta(get_the_ID(), 'listing_email_1', true);
            $listing_website = get_post_meta(get_the_ID(), 'listing_website', true);
            $listing_address = get_post_meta(get_the_ID(), 'listing_full_address', true);
            $listing_city = get_post_meta(get_the_ID(), 'listing_city', true);
            $listing_state = get_post_meta(get_the_ID(), 'listing_state', true);
            $listing_rating = get_post_meta(get_the_ID(), 'listing_rating', true);
            $listing_reviews = get_post_meta(get_the_ID(), 'listing_reviews', true);
            $listing_reviews_link = get_post_meta(get_the_ID(), 'listing_reviews_link', true);
            $listing_photo = get_post_meta(get_the_ID(), 'listing_photo', true);
            $listing_about = get_post_meta(get_the_ID(), 'listing_about', true);
            $listing_working_hours = get_post_meta(get_the_ID(), 'listing_working_hours', true);
            $listing_booking_link = get_post_meta(get_the_ID(), 'listing_booking_appointment_link', true);
            $listing_verified = get_post_meta(get_the_ID(), 'listing_verified', true);
            
            // Get directory terms
            $directories = get_the_terms(get_the_ID(), 'directory');
            
            // Get social media links
            $social_links = get_social_media_links(get_the_ID());
            
            // Format working hours
            $formatted_hours = format_working_hours($listing_working_hours);
            ?>
            
            <article id="post-<?php the_ID(); ?>" <?php post_class('single-listing'); ?>>
                
                <!-- Listing Header -->
                <header class="single-listing-header">
                    <div class="listing-header-content">
                        <div class="listing-main-info">
                            <?php edit_post_link(); ?>
                            <h1 class="listing-title">
                                <?php echo esc_html($listing_name); ?>
                                <?php if ($listing_verified) : ?>
                                    <span class="verified-badge">✓ Verified</span>
                                <?php endif; ?>
                            </h1>
                            
                            <?php if ($directories && !is_wp_error($directories)) : ?>
                                <div class="listing-categories">
                                    <?php foreach ($directories as $directory) : ?>
                                        <span class="category-tag">
                                            <a href="<?php echo esc_url(get_term_link($directory)); ?>">
                                                <?php echo esc_html($directory->name); ?>
                                            </a>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($listing_rating) : ?>
                                <div class="listing-rating">
                                    <span class="stars"><?php echo get_rating_stars($listing_rating); ?></span>
                                    <span class="rating-text">
                                        <?php echo esc_html($listing_rating); ?> out of 5
                                        <?php if ($listing_reviews) : ?>
                                            (<?php echo esc_html($listing_reviews); ?> reviews)
                                        <?php endif; ?>
                                    </span>
                                    <?php if ($listing_reviews_link) : ?>
                                        <a href="<?php echo esc_url($listing_reviews_link); ?>" target="_blank" class="reviews-link">
                                            Read Reviews
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($listing_photo) : ?>
                            <div class="listing-header-image">
                                <img src="<?php echo esc_url($listing_photo); ?>" alt="<?php echo esc_attr($listing_name); ?>">
                            </div>
                        <?php endif; ?>
                    </div>
                </header>
                
                <!-- Listing Content Grid -->
                <div class="listing-info-grid">
                    
                    <!-- Main Content -->
                    <div class="listing-details">
                        
                        <?php if ($listing_about || get_the_content()) : ?>
                            <div class="info-section">
                                <h3>About</h3>
                                <?php if ($listing_about) : ?>
                                    <p><?php echo wp_kses_post($listing_about); ?></p>
                                <?php endif; ?>
                                
                                <?php if (get_the_content()) : ?>
                                    <div class="listing-content">
                                        <?php the_content(); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($formatted_hours)) : ?>
                            <div class="info-section">
                                <h3>Working Hours</h3>
                                <ul class="working-hours">
                                    <?php foreach ($formatted_hours as $day => $hours) : ?>
                                        <li>
                                            <span class="day"><?php echo esc_html(ucfirst($day)); ?></span>
                                            <span class="hours <?php echo ($hours === 'Closed') ? 'closed' : 'open'; ?>">
                                                <?php echo esc_html($hours); ?>
                                            </span>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($social_links)) : ?>
                            <div class="info-section">
                                <h3>Follow Us</h3>
                                <div class="social-links">
                                    <?php foreach ($social_links as $platform => $url) : ?>
                                        <a href="<?php echo esc_url($url); ?>" target="_blank" class="social-link" rel="noopener noreferrer nofollow">
                                            <?php echo esc_html($platform); ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                    </div>
                    
                    <!-- Sidebar -->
                    <div class="listing-sidebar">
                        
                        <!-- Contact Information -->
                        <div class="info-section">
                            <h3>Contact Information</h3>
                            <ul class="contact-info">
                                <?php if ($listing_address) : ?>
                                    <li>
                                        <strong>📍 Address:</strong><br>
                                        <?php echo esc_html($listing_address); ?>
                                    </li>
                                <?php endif; ?>
                                
                                <?php if ($listing_phone) : ?>
                                    <li>
                                        <strong>📞 Phone:</strong><br>
                                        <a href="tel:<?php echo esc_attr($listing_phone); ?>">
                                            <?php echo esc_html($listing_phone); ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php if ($listing_website) : ?>
                                    <li>
                                        <strong>🌐 Website:</strong><br>
                                        <a href="<?php echo esc_url($listing_website); ?>" target="_blank" rel="noopener noreferrer nofollow">
                                            Visit Website
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="info-section">
                            <h3>Quick Actions</h3>
                            <div class="action-buttons">
                                <?php if ($listing_phone) : ?>
                                    <a href="tel:<?php echo esc_attr($listing_phone); ?>" class="btn">
                                        📞 Call Now
                                    </a>
                                <?php endif; ?>
                                
                                <?php if ($listing_booking_link) : ?>
                                    <a href="<?php echo esc_url($listing_booking_link); ?>" target="_blank" class="btn">
                                        📅 Book Appointment
                                    </a>
                                <?php endif; ?>
                                
                                <?php if ($listing_address) : ?>
                                    <a href="https://maps.google.com/?q=<?php echo urlencode($listing_address); ?>" target="_blank" class="btn btn-secondary" rel="noopener noreferrer nofollow">
                                        🗺️ Get Directions
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                    </div>
                    
                </div>
                
            </article>
            
        <?php endwhile; ?>
        
    </div><!-- .container -->
</main><!-- #main -->

<?php
get_footer();
