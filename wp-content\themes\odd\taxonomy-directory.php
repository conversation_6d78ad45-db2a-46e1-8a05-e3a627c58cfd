<?php
/**
 * The template for displaying directory taxonomy archives
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        
        <?php if (have_posts()) : ?>
            
            <header class="page-header">
                <?php
                $term = get_queried_object();
                ?>
                <h1 class="page-title">
                    <?php echo esc_html($term->name); ?> in Odessa
                </h1>
                
                <?php if ($term->description) : ?>
                    <div class="taxonomy-description">
                        <?php echo wp_kses_post($term->description); ?>
                    </div>
                <?php endif; ?>
                
                <div class="archive-meta">
                    <p>Found <?php echo $wp_query->found_posts; ?> dental practices offering <?php echo esc_html($term->name); ?> services</p>
                </div>
            </header><!-- .page-header -->

            <!-- Search and Filter Section -->
            <div class="search-filters">
                <form method="get" action="<?php echo esc_url(get_term_link($term)); ?>" class="directory-search-form">
                    <div class="filter-row">
                        <input type="search" 
                               name="s" 
                               value="<?php echo get_search_query(); ?>" 
                               placeholder="Search within <?php echo esc_attr($term->name); ?>..." 
                               class="search-input">
                        
                        <select name="listing_city" class="filter-select">
                            <option value="">All Cities</option>
                            <?php
                            // Get unique cities from listings in this taxonomy
                            global $wpdb;
                            $cities = $wpdb->get_col($wpdb->prepare("
                                SELECT DISTINCT pm.meta_value 
                                FROM {$wpdb->postmeta} pm
                                INNER JOIN {$wpdb->term_relationships} tr ON pm.post_id = tr.object_id
                                INNER JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id
                                WHERE pm.meta_key = 'listing_city' 
                                AND pm.meta_value != '' 
                                AND tt.term_id = %d
                                ORDER BY pm.meta_value ASC
                            ", $term->term_id));
                            
                            $selected_city = get_query_var('listing_city');
                            foreach ($cities as $city) :
                                $selected = ($selected_city == $city) ? 'selected' : '';
                            ?>
                                <option value="<?php echo esc_attr($city); ?>" <?php echo $selected; ?>>
                                    <?php echo esc_html($city); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <select name="listing_rating" class="filter-select">
                            <option value="">All Ratings</option>
                            <?php
                            $selected_rating = get_query_var('listing_rating');
                            for ($i = 5; $i >= 1; $i--) :
                                $selected = ($selected_rating == $i) ? 'selected' : '';
                            ?>
                                <option value="<?php echo $i; ?>" <?php echo $selected; ?>>
                                    <?php echo $i; ?>+ Stars
                                </option>
                            <?php endfor; ?>
                        </select>
                        
                        <button type="submit" class="search-button">Filter Results</button>
                    </div>
                </form>
            </div>

            <div class="directory-grid">
                <?php
                while (have_posts()) :
                    the_post();
                    
                    // Get listing data (updated field names)
                    $listing_name = get_the_title(); // Use post title instead of listing_name
                    $listing_phone = get_post_meta(get_the_ID(), 'listing_phone', true);
                    $listing_address = get_post_meta(get_the_ID(), 'listing_full_address', true);
                    $listing_city = get_post_meta(get_the_ID(), 'listing_city', true);
                    $listing_rating = get_post_meta(get_the_ID(), 'listing_rating', true);
                    $listing_reviews = get_post_meta(get_the_ID(), 'listing_reviews', true);
                    $listing_photo = get_post_meta(get_the_ID(), 'listing_photo', true);
                    $listing_about = get_post_meta(get_the_ID(), 'listing_about', true);
                    
                    // Get all directory terms for this listing
                    $directories = get_the_terms(get_the_ID(), 'directory');
                ?>
                    <article class="listing-card">
                        <?php if ($listing_photo) : ?>
                            <div class="listing-image">
                                <img src="<?php echo esc_url($listing_photo); ?>" alt="<?php echo esc_attr($listing_name); ?>">
                            </div>
                        <?php endif; ?>
                        
                        <div class="listing-content">
                            <h3 class="listing-title">
                                <a href="<?php the_permalink(); ?>">
                                    <?php echo esc_html($listing_name); ?>
                                </a>
                            </h3>
                            
                            <?php if ($directories && !is_wp_error($directories)) : ?>
                                <div class="listing-meta">
                                    <?php
                                    $directory_names = array();
                                    foreach ($directories as $directory) {
                                        $directory_names[] = $directory->name;
                                    }
                                    echo esc_html(implode(', ', $directory_names));
                                    ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($listing_address) : ?>
                                <div class="listing-address">
                                    📍 <?php echo esc_html($listing_address); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($listing_rating) : ?>
                                <div class="listing-rating">
                                    <span class="stars"><?php echo get_rating_stars($listing_rating); ?></span>
                                    <span class="rating-text">
                                        <?php echo esc_html($listing_rating); ?>
                                        <?php if ($listing_reviews) : ?>
                                            (<?php echo esc_html($listing_reviews); ?> reviews)
                                        <?php endif; ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                            
                            <div class="listing-actions">
                                <a href="<?php the_permalink(); ?>" class="btn">View Details</a>
                            </div>
                        </div>
                    </article>
                <?php endwhile; ?>
            </div>

            <?php
            // Pagination
            the_posts_pagination(array(
                'mid_size'  => 2,
                'prev_text' => __('Previous', 'odd'),
                'next_text' => __('Next', 'odd'),
            ));
            ?>

        <?php else : ?>

            <section class="no-results not-found">
                <header class="page-header">
                    <h1 class="page-title">
                        No <?php echo esc_html($term->name); ?> providers found
                    </h1>
                </header><!-- .page-header -->

                <div class="page-content">
                    <p>Sorry, we couldn't find any dental practices offering <?php echo esc_html($term->name); ?> services at the moment.</p>
                    <p>Try browsing other dental services or <a href="<?php echo esc_url(home_url('/')); ?>">return to the homepage</a>.</p>
                    
                    <?php
                    // Show other directory terms
                    $other_directories = get_terms(array(
                        'taxonomy' => 'directory',
                        'hide_empty' => true,
                        'exclude' => array($term->term_id),
                        'number' => 6
                    ));
                    
                    if (!empty($other_directories) && !is_wp_error($other_directories)) :
                    ?>
                        <h3>Browse Other Dental Services:</h3>
                        <div class="other-categories">
                            <?php foreach ($other_directories as $other_directory) : ?>
                                <a href="<?php echo esc_url(get_term_link($other_directory)); ?>" class="btn btn-secondary">
                                    <?php echo esc_html($other_directory->name); ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div><!-- .page-content -->
            </section><!-- .no-results -->

        <?php endif; ?>
        
    </div><!-- .container -->
</main><!-- #main -->

<?php
get_footer();
