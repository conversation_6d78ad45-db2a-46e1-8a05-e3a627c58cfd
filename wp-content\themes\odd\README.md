# Odessa Dentists Theme

A custom WordPress theme designed specifically for dental directory websites.

## Theme Information

- **Theme Name:** Odessa Dentists
- **Theme Directory:** `odd`
- **Version:** 1.0
- **Description:** Custom WordPress theme for Odessa dental directory website

## Features

### Custom Post Types & Taxonomies
- **Custom Post Type:** `listing` (for individual dental practices/dentists)
- **Custom Taxonomy:** `directory` (for dental service categories)

### Custom Fields
All custom fields use the `listing_` prefix and include:

#### Basic Information
- listing_website, listing_subtypes, listing_category, listing_type

#### Contact Information
- listing_phone, listing_email_1, listing_email_1_full_name
- listing_email_1_first_name, listing_email_1_last_name
- listing_email_1_title, listing_email_1_phone

#### Location Data
- listing_full_address, listing_borough, listing_street, listing_city
- listing_zip, listing_state, listing_country
- listing_lat, listing_long, listing_h3, listing_area_service

#### Business Information
- listing_rating, listing_reviews, listing_reviews_link
- listing_photo, listing_street_view
- listing_about, listing_logo, listing_verified

#### Links & IDs
- listing_booking_appointment_link, listing_location_link
- listing_location_reviews_link, listing_place_id, listing_google_id
- listing_cid, listing_kgmid, listing_reviews_id

#### Social Media
- listing_facebook, listing_instagram, listing_linkedin
- listing_tiktok, listing_twitter, listing_youtube

#### Working Hours
- listing_working_hours (JSON format)
- listing_popular_times (JSON format)

### Working Hours JSON Format
```json
{
  "work_hours": {
    "timetable": {
      "sunday": [{"open": {"hour": 0, "minute": 0}, "close": {"hour": 0, "minute": 0}}],
      "monday": [{"open": {"hour": 9, "minute": 0}, "close": {"hour": 17, "minute": 0}}],
      "tuesday": [{"open": {"hour": 9, "minute": 0}, "close": {"hour": 17, "minute": 0}}],
      "wednesday": [{"open": {"hour": 9, "minute": 0}, "close": {"hour": 17, "minute": 0}}],
      "thursday": [{"open": {"hour": 9, "minute": 0}, "close": {"hour": 17, "minute": 0}}],
      "friday": [{"open": {"hour": 9, "minute": 0}, "close": {"hour": 17, "minute": 0}}],
      "saturday": [{"open": {"hour": 0, "minute": 0}, "close": {"hour": 0, "minute": 0}}]
    },
    "current_status": "open"
  }
}
```

## Template Files

### Core Templates
- `style.css` - Main stylesheet with responsive design
- `functions.php` - Theme functions and custom post type registration
- `header.php` - Site header template
- `footer.php` - Site footer template
- `index.php` - Main template file (fallback)

### Specialized Templates
- `front-page.php` - Homepage template with hero section and featured listings
- `single-listing.php` - Individual dentist/practice profile page
- `taxonomy-directory.php` - Archive page for dental service categories
- `archive-listing.php` - Archive page for all listings
- `search.php` - Search results template

### JavaScript
- `js/main.js` - Enhanced functionality including mobile menu, smooth scrolling, and form enhancements

## Functionality

### Search & Filtering
- Advanced search functionality for listings
- Filter by city, rating, and dental service categories
- Custom query variables for filtering
- AJAX search capability (optional)

### Display Features
- Responsive grid layout for listing cards
- Star rating display system
- Working hours formatting and display
- Social media links integration
- Contact information display
- Verified badge system

### Admin Features
- Custom meta boxes for easy data entry
- Organized field groups (Basic Info, Contact, Location, Business, Social, Hours)
- JSON validation for working hours
- Comprehensive field sanitization

## Installation

1. Upload the `odd` folder to `/wp-content/themes/`
2. Activate the theme in WordPress admin
3. The custom post type and taxonomy will be automatically registered
4. Start adding dental listings through the admin panel

## Usage

### Adding Listings
1. Go to "Dental Listings" in the WordPress admin
2. Click "Add New"
3. Fill in the listing information using the custom meta boxes
4. Assign appropriate directory categories
5. Publish the listing

### Managing Categories
1. Go to "Dental Listings" > "Directories"
2. Add new dental service categories
3. Assign descriptions to help with SEO

### Customization
- Modify `style.css` for design changes
- Edit template files for layout modifications
- Add custom functionality in `functions.php`

## Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Responsive design for mobile devices
- Progressive enhancement for older browsers

## Dependencies
- WordPress 5.0+
- PHP 7.4+
- jQuery (included with WordPress)

## License
Custom development for Odessa Dentists directory website.
